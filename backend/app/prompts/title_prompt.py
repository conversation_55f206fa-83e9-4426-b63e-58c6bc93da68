import json
from backend.app.config.config import JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID, YIYAO_SAAS_WORKSPACE_ID, MIYING_WORKSPACE_ID, DAOZHEN_WORKSPACE_ID
import copy
def get_bug_title_three_element_need_prompt(bug_data, workspace_id) -> str:
    """三要素必要性判断 Prompt：严格规则 + 思维链输出"""
    return f"""
你是一位资深缺陷标题分析专家。请先**逐步推理**（思维链），再给出最终结论，最终仅以 JSON 格式输出。

# 🎯 任务目标
1. 基于缺陷数据理解缺陷的本质与触发流程；
2. 按照判断原则，逐项判断标题是否必须包含三要素（操作位置、操作行为、异常现象）；
3. 在 `thinking_process` 中，逐条比对判断原则，解释为什么要/不要该要素；
4. 输出结构化结果。

# 输入信息
=====
{json.dumps(bug_data, ensure_ascii=False, indent=4)}
=====

# 📘三要素定义
1. 操作位置：问题发生的位置，用于明确异常上下文；  
2. 操作行为：用户在操作位置上执行的动作，通常是导致异常的触发动作；  
3. 异常现象：操作行为后系统的不符合预期的表现，**必须体现**。

# ✅ 判断原则
### 操作位置
- 可省略情况：  
  1) 问题本质是配置的浮窗/弹窗等全局组件，与特定页面位置无关；  
  2) 系统接口/数据/服务端直接异常，与页面弱关联；  
  3) 异常现象本身已能准确定位问题，缺失位置不影响理解；  
  4) 组件通用异常（浮窗按钮、弹窗文案等），异常在多个页面可出现；  
- 必须体现情况：  
  - 存在多个可能的操作行为时；  
- 特殊说明：  
  - 可用泛指术语（如“个人中心”）；  
  - 控件级别不算操作位置；  
  - 业务前缀（如“【腾讯健康】”）不计入操作位置。

### 异常现象
- 核心，必须体现；  
- 应准确描述系统表现。

### 操作行为
- 可省略的情况：判断是否为异常暴露的必要触发前提：  
  1) 若只是自然浏览流程（进入页面、查看内容等）或异常本质为后台波动，则不需要；  
  2) 若异常现象本身已能表示问题本质，也不需要；  
  3) 若异常依赖某个特定操作触发，则需要。  

# ⛔ 输出要求
只输出 JSON，包含以下字段：
{{
  "thinking_process": "逐步推理过程，逐条引用上述规则说明取舍逻辑",
  "needed_elements": ["异常现象"] 或 ["操作位置","异常现象"] 或 ["操作位置","操作行为","异常现象"],
  "reason": "简明总结判定原因（≤60字）"
}}

# 📚 示例

示例1：位置无关异常，可省略操作位置
{{
  "thinking_process": "1. 缺陷为浮窗按钮文案未限制；2. 属全局组件，满足位置可省略规则(1)；3. 标题中已有异常现象；4. 无需操作行为。",
  "needed_elements": ["异常现象"],
  "reason": "浮窗全局组件异常，位置可省略"
}}

示例2：操作行为必须体现
{{
  "thinking_process": "1. 缺陷为搜索敏感词后样式异常；2. 搜索是必要触发动作，未体现操作行为；3. 属多动作场景，需要操作位置；4. 因此需补充操作位置与操作行为。",
  "needed_elements": ["操作位置","操作行为","异常现象"],
  "reason": "异常依赖搜索操作触发"
}}
"""

# def get_bug_title_quality_prompt_stage1(bug_data, three_elements_result, workspace_id) -> str:
#     """标题质量评估第一阶段：基于三要素判断结果，对标题中的必要要素进行评分与反馈"""
#     title_prefix_standard_dict = {
#         JIANKANG_WORKSPACE_ID: "健康业务标题应以“【腾讯健康】”作为前缀",
#         YIBAO_WORKSPACE_ID: "标题中使用【】括号包裹的内容，不要进行修改；你需要优化标题的正文内容，使其符合规范。",
#     }
#     title_prefix_standard = title_prefix_standard_dict.get(workspace_id, "默认标题前缀规范")
#     bug_data_copy = copy.deepcopy(bug_data)
#     title = bug_data_copy.pop("标题")
#     title_work_position = ""
#     if workspace_id != JIANKANG_WORKSPACE_ID:
#         title_work_position = "标题的操作位置可以参考标题前缀，如【会议】"
#     else:
#         title_work_position = "标题中的业务前缀（如“【腾讯健康】”）不计入操作位置。"
#     return f"""
# # 🎯 背景 #
# 你是一位经验丰富的缺陷标题质量评估专家，擅长分析用户上传的缺陷报告中的“标题”字段质量，并给出合理评分。

# # 🧠 目标 & 思维链 #
# 你需要严格按照以下步骤，对缺陷标题及其上下文进行分析与评估：
# ## Step 1. 标题要素初判（不依赖任何上下文信息）##
# - 仅依据标题本身（不结合缺陷上下文或 three_elements_result），判断标题中是否包含三要素信息：
#     - 操作位置（用户进行操作的界面、模块等）
#     - 操作行为（执行了什么操作）
#     - 异常现象（系统或页面出现了什么问题）
    
# ## Step 2. 缺失要素的必要性分析（结合上下文 + three_elements_result）##
# - ⚠️ three_elements_result 是一个列表，表示该缺陷中哪些三要素是必须的。
# - 若某要素出现在列表中（如 "操作位置"），表示它是必要的；缺失时需要补充。
# - 若某要素未出现在列表中（如 "操作行为"），则可以认为是非必要的，可忽略缺失。
# 1. 查找标题中缺失的三要素；
# 2. 对照 three_elements_result 中的要求判断：
#     - 若缺失的要素在 three_elements_result 中未被标记为必要，则可忽略；
#     - 若缺失的要素在 three_elements_result 中被标记为必要，则需要结合上下文信息，分析是否有必要在标题中补充；

# ## Step 3. 表达评估 ##
# - 判断标题语言是否清晰、专业、通顺、易理解；
# - 若存在以下问题需指出并扣分（用于清晰性维度）：
#     - 模糊、歧义、不通顺；
#     - 用词不规范、不专业；
#     - 缺少核心信息导致理解困难。

# # 📄 输入信息 #
# 1. 缺陷标题（原始标题）：
# =====
# {title}
# =====

# 2. 缺陷上下文，你可以基于此理解缺陷本质：
# =====
# {json.dumps(bug_data_copy, ensure_ascii=False, indent=4)}
# =====

# 3. 上一步中三要素判断结果（three-elements_result），three-elements_result 是一个针对当前缺陷上下文分析后的结果，代表当前缺陷必须包含哪些三要素。列表中存在的要素说明是必要的，若缺失应视为不完整；未出现在列表中的要素可以视为非必要的，不要求填写。
# =====
# {json.dumps(three_elements_result, ensure_ascii=False, indent=4)}
# =====

# # ✅ 三要素说明 #
# 1. 操作位置：用户在执行任何操作前最初所在的页面、模块或功能入口，是问题流程的起点，用于明确异常出现的上下文。
# 2. 操作行为：用户在操作位置上执行的动作，**通常是导致异常暴露的触发动作**，但并非总是根因。
# 3. 异常现象：操作行为执行后，系统出现的不符合预期的表现，是问题最终暴露的形式。

# # 📌 三要素判断原则 #
# 1. **操作位置**（默认必须体现，特殊情况可省略）：
#     a. 可省略的情况：
#         i. 若异常属于**浮窗/弹窗等全局组件**，可配置在任意页面触发，**与特定页面无关**，此类“位置无关异常”可省略。
#         ii. 若异常属于**系统接口异常、数据错误、服务端问题等系统侧的直接异常**，**与页面无强绑定**，也可省略操作位置。
#         iii. 若异常现象已能准确定位问题，且缺失位置不影响理解，可省略。
#         iv. 若缺陷为“组件通用异常”（如浮窗按钮、弹窗文案等），其异常在多个页面都可能出现，无需绑定特定操作位置，此类异常可认定为**“位置无关异常”**，标题中可省略操作位置。
#     b. 必须体现的情况：
#        i. 如果存在多个操作行为，应该需保留操作位置。
#     c. 特殊情况：
#         i. 可使用泛指术语（如“业务侧”、“个人中心”），但控件级别不可视为操作位置。
#         ii. 业务前缀不计入操作位置，例如“【腾讯健康】”。

# 2. **异常现象**（必须体现）：
#    a. 缺陷核心，不可省略；
#    b. 应准确描述暴露出的系统问题表现。

# 3. **操作行为**：
#    a. 若标题中已体现操作行为，直接视为必要要素。
#    b. 若未体现，则判断是否为异常暴露的**必要触发前提**：
#         i. 如果只是自然浏览流程如“进入xx页面”、“用户查看xx内容”等用户常规操作内容，或缺陷异常本质为后台系统/接口波动导致的问题，则不需要操作行为。
#         ii. 若异常现象已经可以表示问题的本质，则不需要操作行为。

# # 评分规则 #
# 1. 标题前缀评分判断
#     a. {title_prefix_standard}
#     b. 若标题前缀不符合所属业务的规范，视为清晰性扣0.4分。

# 2. 标题内容评分：
#     a. 准确性（满分1.0分）：
#         i. 标题中若已体现该要素则直接通过不扣分，无需再判断其必要性。
#         ii. 若标题中未体现该要素 → 再结合 three_elements_result 判断是否属于“必须体现”的必要信息**，若是，扣 0.4分。
#     b. 清晰性（满分1.0分）：
#         i. 轻度问题（如略显口语）扣 0.2。
#         ii. 严重问题（如语义混乱、有语病、不好理解）扣 0.4。

# # 注意事项 #
# 1. 若缺陷本质为某个页面或者某个功能异常，但并没有绝对的触发路径，那么只需异常现象即可。
# 2. 若标题中缺少关键信息，但结合缺陷上下文可以理解，也视为不通过。
# 3. {title_work_position}
# 4. 若标题中体现的操作行为没有完全表达缺陷的操作流程，应以标题中的操作行为为主。
# 5. 能明确表示清楚发生的问题本质即可视为通过。 
# 6. 若某个维度未扣分，不要在 `feedback` 中输出该维度。
# 7. `feedback` 中仅包含实际发生扣分的维度及扣分原因，若该纬度为1.0分则不包含。
# 8. `dimension_scores` 中每个维度仍需输出分数（float）。

# # 📤 输出示例（仅输出 JSON，禁止任何解释性内容）#
# {{
#     "thinking_process": "详细中文思维链过程。先判断标题中三要素是否体现，缺失项再结合三要素判断结果评估其必要性，最后进行语言表达评估与打分",
#     "dimension_scores": {{
#         "准确性": float,
#         "清晰性": float
#     }},
#     "feedback": "准确性 -X：原因；清晰性 -X：原因"
# }}
# """


def get_bug_title_quality_prompt_stage1(bug_data, three_elements_result, workspace_id) -> str:
    """标题质量评估第一阶段：基于三要素结果，对标题必要性和表达进行评分与反馈"""
    title_prefix_standard_dict = {
        JIANKANG_WORKSPACE_ID: "健康业务标题应以“【腾讯健康】”作为前缀",
        YIBAO_WORKSPACE_ID: "标题中使用【】括号包裹的内容，不要进行修改；你需要优化标题的正文内容，使其符合规范。",
    }
    title_prefix_standard = title_prefix_standard_dict.get(workspace_id, "默认标题前缀规范")
    bug_data_copy = copy.deepcopy(bug_data)
    title = bug_data_copy.pop("标题")
    title_work_position = ""
    if workspace_id != JIANKANG_WORKSPACE_ID:
        title_work_position = "标题的操作位置可以参考标题前缀，如【会议】"
    else:
        title_work_position = "标题中的业务前缀（如“【腾讯健康】”）不计入操作位置。"
    return f"""
# 🎯 背景 #
你是一位经验丰富的缺陷标题质量评估专家，负责分析用户上传的缺陷报告中的“标题”字段质量，并给出合理评分。

# 🧠 任务目标 #
你需要按照以下步骤，对缺陷标题进行评估：
## Step 1. 标题要素初判 ##
- 仅依据标题本身（不结合上下文），判断是否包含三要素信息：
  1. 操作位置
  2. 操作行为
  3. 异常现象

## Step 2. 缺失要素必要性分析 ##
- 对照 three_elements_result（表示该缺陷必须包含的三要素列表）：
  - 若缺失的要素在 three_elements_result 中 → 扣分；
  - 若缺失的要素不在 three_elements_result 中 → 可忽略。

## Step 3. 表达评估 ##
- 判断标题语言是否清晰、专业、通顺；
- 若存在以下问题需指出并扣分（用于清晰性维度）：
  - 模糊、歧义、不通顺；
  - 用词不规范、不专业；
  - 缺少核心信息导致理解困难。

# 📄 输入信息 #
1. 缺陷标题（原始标题）：
=====
{title}
=====

2. 缺陷上下文（仅用于辅助理解，不要过度依赖）：
=====
{json.dumps(bug_data_copy, ensure_ascii=False, indent=4)}
=====

3. 三要素必要性结果（three_elements_result，列表）：
=====
{json.dumps(three_elements_result, ensure_ascii=False, indent=4)}
=====

# 评分规则 #
1. 标题前缀评分：
   a. {title_prefix_standard}
   b. 若前缀不符合规范 → 清晰性扣 0.4。

2. 标题内容评分：
   a. 准确性（满分1.0）：
      - 标题中缺失的要素若在 three_elements_result 中是必须 → 扣 0.4。
   b. 清晰性（满分1.0）：
      - 轻度问题（略口语/轻微歧义） → 扣 0.2。
      - 严重问题（语义混乱/难理解） → 扣 0.4。

# 注意事项 #
1. {title_work_position}
2. 若某维度未扣分，不要在 `feedback` 中输出该维度。
3. `feedback` 中仅包含实际扣分点，按“维度 -X：原因”形式书写。
4. `dimension_scores` 中每个维度仍需输出分数（float）。

# 📤 输出示例（仅输出 JSON，禁止任何解释性内容）#
{{
    "thinking_process": "详细中文思维链过程。先判断标题包含的三要素，再结合 three_elements_result 判断缺失是否必要，最后进行清晰度检查与打分。",
    "dimension_scores": {{
        "准确性": 0.6,
        "清晰性": 0.8
    }},
    "feedback": "准确性 -0.4：缺少必要操作位置；清晰性 -0.2：标题用词略显口语"
}}
"""


def get_bug_title_quality_prompt_stage2(bug_data, stage1_result, workspace_id) -> str:
    """标题质量评估第二阶段：基于第一步三要素和第一阶段评分结果，生成高质量建议标题"""
    bug_data_copy = copy.deepcopy(bug_data)
    title = bug_data_copy.pop("标题", "")
    title_prefix_standard_dict = {
        JIANKANG_WORKSPACE_ID: "健康业务标题应以“【腾讯健康】”作为前缀",
        YIBAO_WORKSPACE_ID: "标题中使用【】括号包裹的内容，不要进行修改；你需要优化标题的正文内容，使其符合规范。",
        YIYAO_SAAS_WORKSPACE_ID: "标题中使用【】括号包裹的内容为模块信息，可参考但不要进行修改；你需要优化标题的正文内容，使其符合规范。",
        MIYING_WORKSPACE_ID: "标题中使用【】括号包裹的内容为模块信息，可参考但不要进行修改；你需要优化标题的正文内容，使其符合规范。",
        DAOZHEN_WORKSPACE_ID: "标题中使用【】括号包裹的内容为模块信息，可参考但不要进行修改；你需要优化标题的正文内容，使其符合规范。"
    }
    title_prefix_standard = title_prefix_standard_dict.get(workspace_id, "默认标题前缀规范")
    return f"""
# 🎯 背景 #
你是一位资深缺陷标题优化专家，擅长基于缺陷内容与结构化分析结果，生成高质量的缺陷标题。

# 🧠 任务目标 & 思维链 #
请完成以下任务：
1. 分析理解缺陷的本质，明确缺陷暴露的问题发生的根因，理解缺陷想表达的本质。
2. 基于你对缺陷的理解，分析第一阶段标题质量评分结果是否合理
3. 结合缺陷上下文以及你对缺陷的理解和第一阶段评分结果，生成一个高质量的建议标题。

# 📄 输入信息 #
1. 缺陷标题（原始标题）：
=====
{title}
=====

2. 缺陷详情（原始缺陷内容）：
=====
{json.dumps(bug_data_copy, ensure_ascii=False, indent=4)}
=====

3. 第一阶段标题质量评分结果：
=====
{json.dumps(stage1_result, ensure_ascii=False, indent=4)}
=====

# ✅ 三要素说明 #
1. 操作位置：用户在执行任何操作前最初所在的页面、模块或功能入口，是问题流程的起点，用于明确异常出现的上下文。
2. 操作行为：用户在操作位置上执行的动作，**通常是导致异常暴露的触发动作**，但并非总是根因。
3. 异常现象：操作行为执行后，系统出现的不符合预期的表现，是问题最终暴露的形式。

# 📌 标题生成规则 #
1. 内容要求：
    a. {title_prefix_standard}
    b. 不得添加缺陷内容中未提及的额外信息，避免凭空扩展或主观臆测。
    c. 操作行为和异常现象为多个时，应概括表达，不必列举全部细节
    d. 若根据第一步评分结果需要修复操作行为或异常现象：
        i. 若存在多个操作行为或异常现象时，需要判断哪个才是缺陷的核心本质：
            1. 若标题中存在操作行为或异常现象，则应当认为原始标题体现的即为缺陷核心本质。
            2. 若标题中未体现操作行为或异常现象，则应当合理分析缺陷本质并给出建议。
    e. ** 若标题中含有关键信息，在生成建议时不得删除原有的关键信息 **。

2. 表达规范：
    a. 保持标题简洁、专业、无歧义，语言表达清晰，避免口语化或缩略语。
    b. 操作行为和异常现象为多个时，应概括表达，不必列举全部细节：
        i. 若多个异常现象中有**明显核心问题**，应突出核心；
        ii. 若无核心，应笼统描述整个集合。
    c. 若存在“模块归属 + 控件位置”的描述（如“健康问问输入框上方”），应保留模块信息，避免仅写“输入框上方”造成歧义。

# 特别说明 #：
    1. 操作位置需为页面/模块级/tab级，不需要太过详细，能明确出现异常的上下文即可，不得使用控件级表达（如“按钮”、“输入框”）。
    2. 多步骤缺陷中，操作行为应**概括关键步骤**，非列出全部流程；
    3. 异常现象为多个时，应**概括整体或突出核心问题**，避免细节堆砌。

# 📤 输出格式（仅输出 JSON，使用双引号，禁止其他说明）#
{{
    "thinking_process": "1. 首先理解缺陷本质，明确缺陷想表达的重点。2. 分析第一阶段评分结果，判断是否合理。3. 结合缺陷上下文和评分结果，生成高质量建议标题。",
    "suggest": "建议标题"
}}
"""

